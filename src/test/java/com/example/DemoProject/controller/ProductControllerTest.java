package com.example.DemoProject.controller;

import com.example.DemoProject.entity.Product;
import com.example.DemoProject.service.ProductService;
import com.example.DemoProject.util.TestDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ProductController.class)
class ProductControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProductService productService;

    @Autowired
    private ObjectMapper objectMapper;

    private Product testProduct;

    @BeforeEach
    void setUp() {
        testProduct = TestDataBuilder.createTestProduct();
    }

    @Test
    void getAllProducts_ShouldReturnProductList() throws Exception {
        // Given
        List<Product> products = TestDataBuilder.createTestProducts();
        when(productService.getAllProducts()).thenReturn(products);

        // When & Then
        mockMvc.perform(get("/api/products"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].name").value("Laptop"))
                .andExpect(jsonPath("$[1].name").value("Mouse"));

        verify(productService).getAllProducts();
    }

    @Test
    void getActiveProducts_ShouldReturnActiveProducts() throws Exception {
        // Given
        List<Product> activeProducts = TestDataBuilder.createTestProducts();
        when(productService.getActiveProducts()).thenReturn(activeProducts);

        // When & Then
        mockMvc.perform(get("/api/products/active"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(3));

        verify(productService).getActiveProducts();
    }

    @Test
    void getProductById_WhenProductExists_ShouldReturnProduct() throws Exception {
        // Given
        when(productService.getProductById(1L)).thenReturn(Optional.of(testProduct));

        // When & Then
        mockMvc.perform(get("/api/products/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("Test Product"))
                .andExpect(jsonPath("$.price").value(99.99))
                .andExpect(jsonPath("$.stockQuantity").value(100));

        verify(productService).getProductById(1L);
    }

    @Test
    void getProductById_WhenProductNotFound_ShouldReturn404() throws Exception {
        // Given
        when(productService.getProductById(1L)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/products/1"))
                .andExpect(status().isNotFound());

        verify(productService).getProductById(1L);
    }

    @Test
    void createProduct_WhenValidProduct_ShouldCreateProduct() throws Exception {
        // Given
        Product newProduct = TestDataBuilder.createTestProduct("New Product", new BigDecimal("149.99"), 50);
        newProduct.setId(null); // New product shouldn't have ID
        when(productService.createProduct(any(Product.class))).thenReturn(testProduct);

        // When & Then
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newProduct)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("Test Product"));

        verify(productService).createProduct(any(Product.class));
    }

    @Test
    void createProduct_WhenInvalidProduct_ShouldReturn400() throws Exception {
        // Given
        Product invalidProduct = new Product();
        invalidProduct.setPrice(new BigDecimal("-10")); // Invalid negative price

        // When & Then
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidProduct)))
                .andExpect(status().isBadRequest());

        verify(productService, never()).createProduct(any(Product.class));
    }

    @Test
    void updateProduct_WhenValidUpdate_ShouldUpdateProduct() throws Exception {
        // Given
        Product updatedProduct = TestDataBuilder.createTestProduct("Updated Product", new BigDecimal("199.99"), 75);
        when(productService.updateProduct(eq(1L), any(Product.class))).thenReturn(updatedProduct);

        // When & Then
        mockMvc.perform(put("/api/products/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedProduct)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("Updated Product"));

        verify(productService).updateProduct(eq(1L), any(Product.class));
    }

    @Test
    void updateStock_WhenProductExists_ShouldUpdateStock() throws Exception {
        // Given
        when(productService.updateStock(1L, 50)).thenReturn(testProduct);

        // When & Then
        mockMvc.perform(patch("/api/products/1/stock")
                .param("stockQuantity", "50"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(productService).updateStock(1L, 50);
    }

    @Test
    void deactivateProduct_WhenProductExists_ShouldDeactivateProduct() throws Exception {
        // Given
        when(productService.deactivateProduct(1L)).thenReturn(testProduct);

        // When & Then
        mockMvc.perform(patch("/api/products/1/deactivate"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(productService).deactivateProduct(1L);
    }

    @Test
    void activateProduct_WhenProductExists_ShouldActivateProduct() throws Exception {
        // Given
        when(productService.activateProduct(1L)).thenReturn(testProduct);

        // When & Then
        mockMvc.perform(patch("/api/products/1/activate"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(productService).activateProduct(1L);
    }

    @Test
    void searchProducts_ShouldReturnMatchingProducts() throws Exception {
        // Given
        List<Product> products = List.of(testProduct);
        when(productService.searchProducts("Test")).thenReturn(products);

        // When & Then
        mockMvc.perform(get("/api/products/search")
                .param("term", "Test"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Product"));

        verify(productService).searchProducts("Test");
    }

    @Test
    void getProductsByPriceRange_ShouldReturnProductsInRange() throws Exception {
        // Given
        List<Product> products = List.of(testProduct);
        when(productService.getProductsByPriceRange(new BigDecimal("50"), new BigDecimal("150")))
                .thenReturn(products);

        // When & Then
        mockMvc.perform(get("/api/products/price-range")
                .param("minPrice", "50")
                .param("maxPrice", "150"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1));

        verify(productService).getProductsByPriceRange(new BigDecimal("50"), new BigDecimal("150"));
    }

    @Test
    void getLowStockProducts_ShouldReturnLowStockProducts() throws Exception {
        // Given
        List<Product> lowStockProducts = List.of(testProduct);
        when(productService.getLowStockProducts(10)).thenReturn(lowStockProducts);

        // When & Then
        mockMvc.perform(get("/api/products/low-stock")
                .param("threshold", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1));

        verify(productService).getLowStockProducts(10);
    }

    @Test
    void deleteProduct_WhenProductExists_ShouldDeleteProduct() throws Exception {
        // Given
        doNothing().when(productService).deleteProduct(1L);

        // When & Then
        mockMvc.perform(delete("/api/products/1"))
                .andExpect(status().isNoContent());

        verify(productService).deleteProduct(1L);
    }
}
