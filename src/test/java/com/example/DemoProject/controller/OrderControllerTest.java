package com.example.DemoProject.controller;

import com.example.DemoProject.entity.Order;
import com.example.DemoProject.service.OrderService;
import com.example.DemoProject.util.TestDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(OrderController.class)
class OrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OrderService orderService;

    @Autowired
    private ObjectMapper objectMapper;

    private Order testOrder;

    @BeforeEach
    void setUp() {
        testOrder = TestDataBuilder.createTestOrder();
    }

    @Test
    void getAllOrders_ShouldReturnOrderList() throws Exception {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderService.getAllOrders()).thenReturn(orders);

        // When & Then
        mockMvc.perform(get("/api/orders"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].status").value("PENDING"));

        verify(orderService).getAllOrders();
    }

    @Test
    void getOrderById_WhenOrderExists_ShouldReturnOrder() throws Exception {
        // Given
        when(orderService.getOrderById(1L)).thenReturn(Optional.of(testOrder));

        // When & Then
        mockMvc.perform(get("/api/orders/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.status").value("PENDING"))
                .andExpect(jsonPath("$.totalAmount").value(199.98));

        verify(orderService).getOrderById(1L);
    }

    @Test
    void getOrderById_WhenOrderNotFound_ShouldReturn404() throws Exception {
        // Given
        when(orderService.getOrderById(1L)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/orders/1"))
                .andExpect(status().isNotFound());

        verify(orderService).getOrderById(1L);
    }

    @Test
    void getOrderByIdWithItems_ShouldReturnOrderWithItems() throws Exception {
        // Given
        when(orderService.getOrderByIdWithItems(1L)).thenReturn(Optional.of(testOrder));

        // When & Then
        mockMvc.perform(get("/api/orders/1/with-items"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1));

        verify(orderService).getOrderByIdWithItems(1L);
    }

    @Test
    void getOrdersByCustomerId_ShouldReturnCustomerOrders() throws Exception {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderService.getOrdersByCustomerId(1L)).thenReturn(orders);

        // When & Then
        mockMvc.perform(get("/api/orders/customer/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1));

        verify(orderService).getOrdersByCustomerId(1L);
    }

    @Test
    void getOrdersByStatus_ShouldReturnOrdersWithStatus() throws Exception {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderService.getOrdersByStatus(Order.OrderStatus.PENDING)).thenReturn(orders);

        // When & Then
        mockMvc.perform(get("/api/orders/status/PENDING"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1));

        verify(orderService).getOrdersByStatus(Order.OrderStatus.PENDING);
    }

    @Test
    void createOrder_WhenValidOrder_ShouldCreateOrder() throws Exception {
        // Given
        Order newOrder = TestDataBuilder.createTestOrder();
        newOrder.setId(null); // New order shouldn't have ID
        when(orderService.createOrder(any(Order.class))).thenReturn(testOrder);

        // When & Then
        mockMvc.perform(post("/api/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newOrder)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("PENDING"));

        verify(orderService).createOrder(any(Order.class));
    }

    @Test
    void createOrder_WhenInvalidOrder_ShouldReturn400() throws Exception {
        // Given
        Order invalidOrder = new Order();
        // Missing required fields

        // When & Then
        mockMvc.perform(post("/api/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidOrder)))
                .andExpect(status().isBadRequest());

        verify(orderService, never()).createOrder(any(Order.class));
    }

    @Test
    void createOrder_WhenServiceThrowsException_ShouldReturn400() throws Exception {
        // Given
        Order newOrder = TestDataBuilder.createTestOrder();
        newOrder.setId(null);
        when(orderService.createOrder(any(Order.class)))
                .thenThrow(new RuntimeException("Insufficient stock"));

        // When & Then
        mockMvc.perform(post("/api/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newOrder)))
                .andExpect(status().isBadRequest());

        verify(orderService).createOrder(any(Order.class));
    }

    @Test
    void updateOrderStatus_WhenOrderExists_ShouldUpdateStatus() throws Exception {
        // Given
        testOrder.setStatus(Order.OrderStatus.CONFIRMED);
        when(orderService.updateOrderStatus(1L, Order.OrderStatus.CONFIRMED)).thenReturn(testOrder);

        // When & Then
        mockMvc.perform(patch("/api/orders/1/status")
                .param("status", "CONFIRMED"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("CONFIRMED"));

        verify(orderService).updateOrderStatus(1L, Order.OrderStatus.CONFIRMED);
    }

    @Test
    void cancelOrder_WhenOrderExists_ShouldCancelOrder() throws Exception {
        // Given
        doNothing().when(orderService).cancelOrder(1L);

        // When & Then
        mockMvc.perform(patch("/api/orders/1/cancel"))
                .andExpect(status().isOk());

        verify(orderService).cancelOrder(1L);
    }

    @Test
    void cancelOrder_WhenOrderCannotBeCancelled_ShouldReturn400() throws Exception {
        // Given
        doThrow(new RuntimeException("Cannot cancel a delivered order"))
                .when(orderService).cancelOrder(1L);

        // When & Then
        mockMvc.perform(patch("/api/orders/1/cancel"))
                .andExpect(status().isBadRequest());

        verify(orderService).cancelOrder(1L);
    }

    @Test
    void getCustomerOrderHistory_ShouldReturnOrderHistory() throws Exception {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderService.getCustomerOrderHistory(1L)).thenReturn(orders);

        // When & Then
        mockMvc.perform(get("/api/orders/customer/1/history"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1));

        verify(orderService).getCustomerOrderHistory(1L);
    }

    @Test
    void getOrderCountByCustomerId_ShouldReturnCount() throws Exception {
        // Given
        when(orderService.getOrderCountByCustomerId(1L)).thenReturn(5L);

        // When & Then
        mockMvc.perform(get("/api/orders/customer/1/count"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("5"));

        verify(orderService).getOrderCountByCustomerId(1L);
    }

    @Test
    void deleteOrder_WhenOrderExists_ShouldDeleteOrder() throws Exception {
        // Given
        doNothing().when(orderService).deleteOrder(1L);

        // When & Then
        mockMvc.perform(delete("/api/orders/1"))
                .andExpect(status().isNoContent());

        verify(orderService).deleteOrder(1L);
    }

    @Test
    void deleteOrder_WhenOrderNotFound_ShouldReturn404() throws Exception {
        // Given
        doThrow(new RuntimeException("Order not found")).when(orderService).deleteOrder(1L);

        // When & Then
        mockMvc.perform(delete("/api/orders/1"))
                .andExpect(status().isNotFound());

        verify(orderService).deleteOrder(1L);
    }
}
