package com.example.DemoProject.controller;

import com.example.DemoProject.entity.Customer;
import com.example.DemoProject.service.CustomerService;
import com.example.DemoProject.util.TestDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(CustomerController.class)
class CustomerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CustomerService customerService;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer;

    @BeforeEach
    void setUp() {
        testCustomer = TestDataBuilder.createTestCustomer();
    }

    @Test
    void getAllCustomers_ShouldReturnCustomerList() throws Exception {
        // Given
        List<Customer> customers = TestDataBuilder.createTestCustomers();
        when(customerService.getAllCustomers()).thenReturn(customers);

        // When & Then
        mockMvc.perform(get("/api/customers"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].firstName").value("John"))
                .andExpect(jsonPath("$[1].firstName").value("Jane"));

        verify(customerService).getAllCustomers();
    }

    @Test
    void getCustomerById_WhenCustomerExists_ShouldReturnCustomer() throws Exception {
        // Given
        when(customerService.getCustomerById(1L)).thenReturn(Optional.of(testCustomer));

        // When & Then
        mockMvc.perform(get("/api/customers/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.firstName").value("John"))
                .andExpect(jsonPath("$.lastName").value("Doe"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));

        verify(customerService).getCustomerById(1L);
    }

    @Test
    void getCustomerById_WhenCustomerNotFound_ShouldReturn404() throws Exception {
        // Given
        when(customerService.getCustomerById(1L)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/customers/1"))
                .andExpect(status().isNotFound());

        verify(customerService).getCustomerById(1L);
    }

    @Test
    void createCustomer_WhenValidCustomer_ShouldCreateCustomer() throws Exception {
        // Given
        Customer newCustomer = TestDataBuilder.createTestCustomer("Jane", "Smith", "<EMAIL>");
        newCustomer.setId(null); // New customer shouldn't have ID
        when(customerService.createCustomer(any(Customer.class))).thenReturn(testCustomer);

        // When & Then
        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newCustomer)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.firstName").value("John"));

        verify(customerService).createCustomer(any(Customer.class));
    }

    @Test
    void createCustomer_WhenInvalidCustomer_ShouldReturn400() throws Exception {
        // Given
        Customer invalidCustomer = new Customer();
        invalidCustomer.setEmail("invalid-email"); // Invalid email format

        // When & Then
        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidCustomer)))
                .andExpect(status().isBadRequest());

        verify(customerService, never()).createCustomer(any(Customer.class));
    }

    @Test
    void createCustomer_WhenEmailExists_ShouldReturn400() throws Exception {
        // Given
        Customer newCustomer = TestDataBuilder.createTestCustomer();
        newCustomer.setId(null);
        when(customerService.createCustomer(any(Customer.class)))
                .thenThrow(new RuntimeException("Customer with email already exists"));

        // When & Then
        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newCustomer)))
                .andExpect(status().isBadRequest());

        verify(customerService).createCustomer(any(Customer.class));
    }

    @Test
    void updateCustomer_WhenValidUpdate_ShouldUpdateCustomer() throws Exception {
        // Given
        Customer updatedCustomer = TestDataBuilder.createTestCustomer("Jane", "Smith", "<EMAIL>");
        when(customerService.updateCustomer(eq(1L), any(Customer.class))).thenReturn(updatedCustomer);

        // When & Then
        mockMvc.perform(put("/api/customers/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedCustomer)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.firstName").value("Jane"));

        verify(customerService).updateCustomer(eq(1L), any(Customer.class));
    }

    @Test
    void updateCustomer_WhenCustomerNotFound_ShouldReturn404() throws Exception {
        // Given
        Customer updatedCustomer = TestDataBuilder.createTestCustomer();
        when(customerService.updateCustomer(eq(1L), any(Customer.class)))
                .thenThrow(new RuntimeException("Customer not found"));

        // When & Then
        mockMvc.perform(put("/api/customers/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedCustomer)))
                .andExpect(status().isNotFound());

        verify(customerService).updateCustomer(eq(1L), any(Customer.class));
    }

    @Test
    void deleteCustomer_WhenCustomerExists_ShouldDeleteCustomer() throws Exception {
        // Given
        doNothing().when(customerService).deleteCustomer(1L);

        // When & Then
        mockMvc.perform(delete("/api/customers/1"))
                .andExpect(status().isNoContent());

        verify(customerService).deleteCustomer(1L);
    }

    @Test
    void deleteCustomer_WhenCustomerNotFound_ShouldReturn404() throws Exception {
        // Given
        doThrow(new RuntimeException("Customer not found")).when(customerService).deleteCustomer(1L);

        // When & Then
        mockMvc.perform(delete("/api/customers/1"))
                .andExpect(status().isNotFound());

        verify(customerService).deleteCustomer(1L);
    }

    @Test
    void searchCustomers_ShouldReturnMatchingCustomers() throws Exception {
        // Given
        List<Customer> customers = List.of(testCustomer);
        when(customerService.searchCustomers("John")).thenReturn(customers);

        // When & Then
        mockMvc.perform(get("/api/customers/search")
                .param("term", "John"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].firstName").value("John"));

        verify(customerService).searchCustomers("John");
    }

    @Test
    void checkEmailExists_WhenEmailExists_ShouldReturnTrue() throws Exception {
        // Given
        when(customerService.existsByEmail("<EMAIL>")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/customers/exists/email/<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("true"));

        verify(customerService).existsByEmail("<EMAIL>");
    }
}
