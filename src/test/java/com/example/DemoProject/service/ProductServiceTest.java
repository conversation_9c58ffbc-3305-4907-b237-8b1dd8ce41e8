package com.example.DemoProject.service;

import com.example.DemoProject.entity.Product;
import com.example.DemoProject.repository.ProductRepository;
import com.example.DemoProject.util.TestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductServiceTest {

    @Mock
    private ProductRepository productRepository;

    @InjectMocks
    private ProductService productService;

    private Product testProduct;

    @BeforeEach
    void setUp() {
        testProduct = TestDataBuilder.createTestProduct();
    }

    @Test
    void getAllProducts_ShouldReturnAllProducts() {
        // Given
        List<Product> products = TestDataBuilder.createTestProducts();
        when(productRepository.findAll()).thenReturn(products);

        // When
        List<Product> result = productService.getAllProducts();

        // Then
        assertEquals(3, result.size());
        verify(productRepository).findAll();
    }

    @Test
    void getActiveProducts_ShouldReturnOnlyActiveProducts() {
        // Given
        List<Product> activeProducts = TestDataBuilder.createTestProducts();
        when(productRepository.findByActiveTrue()).thenReturn(activeProducts);

        // When
        List<Product> result = productService.getActiveProducts();

        // Then
        assertEquals(3, result.size());
        verify(productRepository).findByActiveTrue();
    }

    @Test
    void getProductById_WhenProductExists_ShouldReturnProduct() {
        // Given
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));

        // When
        Optional<Product> result = productService.getProductById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testProduct.getName(), result.get().getName());
        verify(productRepository).findById(1L);
    }

    @Test
    void createProduct_WhenSkuDoesNotExist_ShouldCreateProduct() {
        // Given
        when(productRepository.existsBySku(testProduct.getSku())).thenReturn(false);
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        Product result = productService.createProduct(testProduct);

        // Then
        assertNotNull(result);
        assertEquals(testProduct.getName(), result.getName());
        verify(productRepository).existsBySku(testProduct.getSku());
        verify(productRepository).save(testProduct);
    }

    @Test
    void createProduct_WhenSkuExists_ShouldThrowException() {
        // Given
        when(productRepository.existsBySku(testProduct.getSku())).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> productService.createProduct(testProduct));
        
        assertTrue(exception.getMessage().contains("already exists"));
        verify(productRepository).existsBySku(testProduct.getSku());
        verify(productRepository, never()).save(any(Product.class));
    }

    @Test
    void updateProduct_WhenProductExists_ShouldUpdateProduct() {
        // Given
        Product updatedDetails = TestDataBuilder.createTestProduct("Updated Product", new BigDecimal("149.99"), 75);
        updatedDetails.setSku("UPD-001");
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));
        when(productRepository.existsBySku(updatedDetails.getSku())).thenReturn(false);
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        Product result = productService.updateProduct(1L, updatedDetails);

        // Then
        assertNotNull(result);
        verify(productRepository).findById(1L);
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void updateStock_WhenProductExists_ShouldUpdateStock() {
        // Given
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        Product result = productService.updateStock(1L, 50);

        // Then
        assertNotNull(result);
        verify(productRepository).findById(1L);
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void deactivateProduct_WhenProductExists_ShouldDeactivateProduct() {
        // Given
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        Product result = productService.deactivateProduct(1L);

        // Then
        assertNotNull(result);
        verify(productRepository).findById(1L);
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void activateProduct_WhenProductExists_ShouldActivateProduct() {
        // Given
        testProduct.setActive(false);
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        Product result = productService.activateProduct(1L);

        // Then
        assertNotNull(result);
        verify(productRepository).findById(1L);
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void getProductsByPriceRange_ShouldReturnProductsInRange() {
        // Given
        List<Product> products = TestDataBuilder.createTestProducts();
        BigDecimal minPrice = new BigDecimal("20.00");
        BigDecimal maxPrice = new BigDecimal("100.00");
        when(productRepository.findByPriceBetween(minPrice, maxPrice)).thenReturn(products);

        // When
        List<Product> result = productService.getProductsByPriceRange(minPrice, maxPrice);

        // Then
        assertEquals(3, result.size());
        verify(productRepository).findByPriceBetween(minPrice, maxPrice);
    }

    @Test
    void getLowStockProducts_ShouldReturnLowStockProducts() {
        // Given
        List<Product> lowStockProducts = List.of(testProduct);
        when(productRepository.findByStockQuantityLessThan(10)).thenReturn(lowStockProducts);

        // When
        List<Product> result = productService.getLowStockProducts(10);

        // Then
        assertEquals(1, result.size());
        verify(productRepository).findByStockQuantityLessThan(10);
    }

    @Test
    void searchProducts_ShouldReturnMatchingProducts() {
        // Given
        List<Product> products = TestDataBuilder.createTestProducts();
        when(productRepository.findBySearchTerm("Laptop")).thenReturn(products);

        // When
        List<Product> result = productService.searchProducts("Laptop");

        // Then
        assertEquals(3, result.size());
        verify(productRepository).findBySearchTerm("Laptop");
    }

    @Test
    void deleteProduct_WhenProductExists_ShouldDeleteProduct() {
        // Given
        when(productRepository.findById(1L)).thenReturn(Optional.of(testProduct));

        // When
        productService.deleteProduct(1L);

        // Then
        verify(productRepository).findById(1L);
        verify(productRepository).delete(testProduct);
    }

    @Test
    void deleteProduct_WhenProductDoesNotExist_ShouldThrowException() {
        // Given
        when(productRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> productService.deleteProduct(1L));
        
        assertTrue(exception.getMessage().contains("not found"));
        verify(productRepository).findById(1L);
        verify(productRepository, never()).delete(any(Product.class));
    }
}
