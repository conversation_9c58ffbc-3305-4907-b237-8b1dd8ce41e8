package com.example.DemoProject.service;

import com.example.DemoProject.entity.Customer;
import com.example.DemoProject.entity.Order;
import com.example.DemoProject.entity.OrderItem;
import com.example.DemoProject.entity.Product;
import com.example.DemoProject.repository.CustomerRepository;
import com.example.DemoProject.repository.OrderItemRepository;
import com.example.DemoProject.repository.OrderRepository;
import com.example.DemoProject.repository.ProductRepository;
import com.example.DemoProject.util.TestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock
    private OrderRepository orderRepository;
    
    @Mock
    private OrderItemRepository orderItemRepository;
    
    @Mock
    private ProductRepository productRepository;
    
    @Mock
    private CustomerRepository customerRepository;

    @InjectMocks
    private OrderService orderService;

    private Order testOrder;
    private Customer testCustomer;
    private Product testProduct;

    @BeforeEach
    void setUp() {
        testCustomer = TestDataBuilder.createTestCustomer();
        testProduct = TestDataBuilder.createTestProduct();
        testOrder = TestDataBuilder.createTestOrder();
    }

    @Test
    void getAllOrders_ShouldReturnAllOrders() {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderRepository.findAll()).thenReturn(orders);

        // When
        List<Order> result = orderService.getAllOrders();

        // Then
        assertEquals(1, result.size());
        verify(orderRepository).findAll();
    }

    @Test
    void getOrderById_WhenOrderExists_ShouldReturnOrder() {
        // Given
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));

        // When
        Optional<Order> result = orderService.getOrderById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testOrder.getId(), result.get().getId());
        verify(orderRepository).findById(1L);
    }

    @Test
    void getOrdersByCustomerId_ShouldReturnCustomerOrders() {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderRepository.findByCustomerId(1L)).thenReturn(orders);

        // When
        List<Order> result = orderService.getOrdersByCustomerId(1L);

        // Then
        assertEquals(1, result.size());
        verify(orderRepository).findByCustomerId(1L);
    }

    @Test
    void getOrdersByStatus_ShouldReturnOrdersWithStatus() {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderRepository.findByStatus(Order.OrderStatus.PENDING)).thenReturn(orders);

        // When
        List<Order> result = orderService.getOrdersByStatus(Order.OrderStatus.PENDING);

        // Then
        assertEquals(1, result.size());
        verify(orderRepository).findByStatus(Order.OrderStatus.PENDING);
    }

    @Test
    void createOrder_WhenValidOrder_ShouldCreateOrder() {
        // Given
        Order orderWithItems = TestDataBuilder.createTestOrderWithItems();
        Customer customer = orderWithItems.getCustomer();
        
        when(customerRepository.findById(customer.getId())).thenReturn(Optional.of(customer));
        
        // Mock products for order items
        for (OrderItem item : orderWithItems.getOrderItems()) {
            Product product = item.getProduct();
            when(productRepository.findById(product.getId())).thenReturn(Optional.of(product));
        }
        when(productRepository.save(any(Product.class))).thenReturn(new Product());
        
        when(orderRepository.save(any(Order.class))).thenReturn(orderWithItems);

        // When
        Order result = orderService.createOrder(orderWithItems);

        // Then
        assertNotNull(result);
        verify(customerRepository).findById(customer.getId());
        verify(orderRepository).save(any(Order.class));
    }

    @Test
    void createOrder_WhenCustomerNotFound_ShouldThrowException() {
        // Given
        when(customerRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> orderService.createOrder(testOrder));
        
        assertTrue(exception.getMessage().contains("Customer not found"));
        verify(customerRepository).findById(1L);
        verify(orderRepository, never()).save(any(Order.class));
    }

    @Test
    void createOrder_WhenProductNotActive_ShouldThrowException() {
        // Given
        Order orderWithItems = TestDataBuilder.createTestOrderWithItems();
        Customer customer = orderWithItems.getCustomer();
        Product inactiveProduct = orderWithItems.getOrderItems().get(0).getProduct();
        inactiveProduct.setActive(false);
        
        when(customerRepository.findById(customer.getId())).thenReturn(Optional.of(customer));
        when(productRepository.findById(inactiveProduct.getId())).thenReturn(Optional.of(inactiveProduct));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> orderService.createOrder(orderWithItems));
        
        assertTrue(exception.getMessage().contains("not active"));
        verify(orderRepository, never()).save(any(Order.class));
    }

    @Test
    void createOrder_WhenInsufficientStock_ShouldThrowException() {
        // Given
        Order orderWithItems = TestDataBuilder.createTestOrderWithItems();
        Customer customer = orderWithItems.getCustomer();
        Product product = orderWithItems.getOrderItems().get(0).getProduct();
        product.setStockQuantity(1); // Less than required quantity (2)
        
        when(customerRepository.findById(customer.getId())).thenReturn(Optional.of(customer));
        when(productRepository.findById(product.getId())).thenReturn(Optional.of(product));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> orderService.createOrder(orderWithItems));
        
        assertTrue(exception.getMessage().contains("Insufficient stock"));
        verify(orderRepository, never()).save(any(Order.class));
    }

    @Test
    void updateOrderStatus_WhenOrderExists_ShouldUpdateStatus() {
        // Given
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));
        when(orderRepository.save(any(Order.class))).thenReturn(testOrder);

        // When
        Order result = orderService.updateOrderStatus(1L, Order.OrderStatus.CONFIRMED);

        // Then
        assertNotNull(result);
        verify(orderRepository).findById(1L);
        verify(orderRepository).save(any(Order.class));
    }

    @Test
    void cancelOrder_WhenOrderExists_ShouldCancelOrder() {
        // Given
        Order orderWithItems = TestDataBuilder.createTestOrderWithItems();
        when(orderRepository.findByIdWithItems(1L)).thenReturn(Optional.of(orderWithItems));
        when(orderRepository.save(any(Order.class))).thenReturn(orderWithItems);
        
        // Mock product saves for stock restoration
        for (OrderItem item : orderWithItems.getOrderItems()) {
            when(productRepository.save(item.getProduct())).thenReturn(item.getProduct());
        }

        // When
        orderService.cancelOrder(1L);

        // Then
        verify(orderRepository).findByIdWithItems(1L);
        verify(orderRepository).save(any(Order.class));
    }

    @Test
    void cancelOrder_WhenOrderDelivered_ShouldThrowException() {
        // Given
        testOrder.setStatus(Order.OrderStatus.DELIVERED);
        when(orderRepository.findByIdWithItems(1L)).thenReturn(Optional.of(testOrder));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> orderService.cancelOrder(1L));
        
        assertTrue(exception.getMessage().contains("Cannot cancel a delivered order"));
        verify(orderRepository, never()).save(any(Order.class));
    }

    @Test
    void getOrderCountByCustomerId_ShouldReturnCount() {
        // Given
        when(orderRepository.countByCustomerId(1L)).thenReturn(5L);

        // When
        Long result = orderService.getOrderCountByCustomerId(1L);

        // Then
        assertEquals(5L, result);
        verify(orderRepository).countByCustomerId(1L);
    }

    @Test
    void getCustomerOrderHistory_ShouldReturnOrderHistory() {
        // Given
        List<Order> orders = List.of(testOrder);
        when(orderRepository.findByCustomerIdOrderByOrderDateDesc(1L)).thenReturn(orders);

        // When
        List<Order> result = orderService.getCustomerOrderHistory(1L);

        // Then
        assertEquals(1, result.size());
        verify(orderRepository).findByCustomerIdOrderByOrderDateDesc(1L);
    }

    @Test
    void deleteOrder_WhenOrderExists_ShouldDeleteOrder() {
        // Given
        when(orderRepository.findById(1L)).thenReturn(Optional.of(testOrder));

        // When
        orderService.deleteOrder(1L);

        // Then
        verify(orderRepository).findById(1L);
        verify(orderRepository).delete(testOrder);
    }
}
