package com.example.DemoProject.service;

import com.example.DemoProject.entity.Customer;
import com.example.DemoProject.repository.CustomerRepository;
import com.example.DemoProject.util.TestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {

    @Mock
    private CustomerRepository customerRepository;

    @InjectMocks
    private CustomerService customerService;

    private Customer testCustomer;

    @BeforeEach
    void setUp() {
        testCustomer = TestDataBuilder.createTestCustomer();
    }

    @Test
    void getAllCustomers_ShouldReturnAllCustomers() {
        // Given
        List<Customer> customers = TestDataBuilder.createTestCustomers();
        when(customerRepository.findAll()).thenReturn(customers);

        // When
        List<Customer> result = customerService.getAllCustomers();

        // Then
        assertEquals(3, result.size());
        verify(customerRepository).findAll();
    }

    @Test
    void getCustomerById_WhenCustomerExists_ShouldReturnCustomer() {
        // Given
        when(customerRepository.findById(1L)).thenReturn(Optional.of(testCustomer));

        // When
        Optional<Customer> result = customerService.getCustomerById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testCustomer.getEmail(), result.get().getEmail());
        verify(customerRepository).findById(1L);
    }

    @Test
    void getCustomerById_WhenCustomerDoesNotExist_ShouldReturnEmpty() {
        // Given
        when(customerRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        Optional<Customer> result = customerService.getCustomerById(1L);

        // Then
        assertFalse(result.isPresent());
        verify(customerRepository).findById(1L);
    }

    @Test
    void createCustomer_WhenEmailDoesNotExist_ShouldCreateCustomer() {
        // Given
        when(customerRepository.existsByEmail(testCustomer.getEmail())).thenReturn(false);
        when(customerRepository.save(any(Customer.class))).thenReturn(testCustomer);

        // When
        Customer result = customerService.createCustomer(testCustomer);

        // Then
        assertNotNull(result);
        assertEquals(testCustomer.getEmail(), result.getEmail());
        verify(customerRepository).existsByEmail(testCustomer.getEmail());
        verify(customerRepository).save(testCustomer);
    }

    @Test
    void createCustomer_WhenEmailExists_ShouldThrowException() {
        // Given
        when(customerRepository.existsByEmail(testCustomer.getEmail())).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> customerService.createCustomer(testCustomer));
        
        assertTrue(exception.getMessage().contains("already exists"));
        verify(customerRepository).existsByEmail(testCustomer.getEmail());
        verify(customerRepository, never()).save(any(Customer.class));
    }

    @Test
    void updateCustomer_WhenCustomerExists_ShouldUpdateCustomer() {
        // Given
        Customer updatedDetails = TestDataBuilder.createTestCustomer("Jane", "Smith", "<EMAIL>");
        when(customerRepository.findById(1L)).thenReturn(Optional.of(testCustomer));
        when(customerRepository.existsByEmail(updatedDetails.getEmail())).thenReturn(false);
        when(customerRepository.save(any(Customer.class))).thenReturn(testCustomer);

        // When
        Customer result = customerService.updateCustomer(1L, updatedDetails);

        // Then
        assertNotNull(result);
        verify(customerRepository).findById(1L);
        verify(customerRepository).save(any(Customer.class));
    }

    @Test
    void updateCustomer_WhenCustomerDoesNotExist_ShouldThrowException() {
        // Given
        Customer updatedDetails = TestDataBuilder.createTestCustomer();
        when(customerRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> customerService.updateCustomer(1L, updatedDetails));
        
        assertTrue(exception.getMessage().contains("not found"));
        verify(customerRepository).findById(1L);
        verify(customerRepository, never()).save(any(Customer.class));
    }

    @Test
    void deleteCustomer_WhenCustomerExists_ShouldDeleteCustomer() {
        // Given
        when(customerRepository.findById(1L)).thenReturn(Optional.of(testCustomer));

        // When
        customerService.deleteCustomer(1L);

        // Then
        verify(customerRepository).findById(1L);
        verify(customerRepository).delete(testCustomer);
    }

    @Test
    void deleteCustomer_WhenCustomerDoesNotExist_ShouldThrowException() {
        // Given
        when(customerRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> customerService.deleteCustomer(1L));
        
        assertTrue(exception.getMessage().contains("not found"));
        verify(customerRepository).findById(1L);
        verify(customerRepository, never()).delete(any(Customer.class));
    }

    @Test
    void searchCustomers_ShouldReturnMatchingCustomers() {
        // Given
        List<Customer> customers = TestDataBuilder.createTestCustomers();
        when(customerRepository.findBySearchTerm("John")).thenReturn(customers);

        // When
        List<Customer> result = customerService.searchCustomers("John");

        // Then
        assertEquals(3, result.size());
        verify(customerRepository).findBySearchTerm("John");
    }

    @Test
    void existsByEmail_WhenEmailExists_ShouldReturnTrue() {
        // Given
        when(customerRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When
        boolean result = customerService.existsByEmail("<EMAIL>");

        // Then
        assertTrue(result);
        verify(customerRepository).existsByEmail("<EMAIL>");
    }
}
