package com.example.DemoProject.util;

import com.example.DemoProject.entity.Customer;
import com.example.DemoProject.entity.Order;
import com.example.DemoProject.entity.OrderItem;
import com.example.DemoProject.entity.Product;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TestDataBuilder {
    
    public static Customer createTestCustomer() {
        Customer customer = new Customer();
        customer.setId(1L);
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setEmail("<EMAIL>");
        customer.setPhoneNumber("1234567890");
        customer.setAddress("123 Main St, City, State");
        customer.setCreatedAt(LocalDateTime.now());
        customer.setUpdatedAt(LocalDateTime.now());
        return customer;
    }
    
    public static Customer createTestCustomer(String firstName, String lastName, String email) {
        Customer customer = createTestCustomer();
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setEmail(email);
        return customer;
    }
    
    public static Product createTestProduct() {
        Product product = new Product();
        product.setId(1L);
        product.setName("Test Product");
        product.setDescription("This is a test product");
        product.setPrice(new BigDecimal("99.99"));
        product.setStockQuantity(100);
        product.setCategory("Electronics");
        product.setSku("TEST-001");
        product.setActive(true);
        product.setCreatedAt(LocalDateTime.now());
        product.setUpdatedAt(LocalDateTime.now());
        return product;
    }
    
    public static Product createTestProduct(String name, BigDecimal price, Integer stock) {
        Product product = createTestProduct();
        product.setName(name);
        product.setPrice(price);
        product.setStockQuantity(stock);
        return product;
    }
    
    public static Product createTestProduct(String name, String sku, BigDecimal price, Integer stock) {
        Product product = createTestProduct(name, price, stock);
        product.setSku(sku);
        return product;
    }
    
    public static Order createTestOrder() {
        Order order = new Order();
        order.setId(1L);
        order.setCustomer(createTestCustomer());
        order.setOrderDate(LocalDateTime.now());
        order.setTotalAmount(new BigDecimal("199.98"));
        order.setStatus(Order.OrderStatus.PENDING);
        order.setShippingAddress("123 Main St, City, State");
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        return order;
    }
    
    public static Order createTestOrder(Customer customer, BigDecimal totalAmount) {
        Order order = createTestOrder();
        order.setCustomer(customer);
        order.setTotalAmount(totalAmount);
        return order;
    }
    
    public static OrderItem createTestOrderItem() {
        OrderItem orderItem = new OrderItem();
        orderItem.setId(1L);
        orderItem.setOrder(createTestOrder());
        orderItem.setProduct(createTestProduct());
        orderItem.setQuantity(2);
        orderItem.setUnitPrice(new BigDecimal("99.99"));
        orderItem.setTotalPrice(new BigDecimal("199.98"));
        return orderItem;
    }
    
    public static OrderItem createTestOrderItem(Order order, Product product, Integer quantity) {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrder(order);
        orderItem.setProduct(product);
        orderItem.setQuantity(quantity);
        orderItem.setUnitPrice(product.getPrice());
        orderItem.setTotalPrice(product.getPrice().multiply(BigDecimal.valueOf(quantity)));
        return orderItem;
    }
    
    public static Order createTestOrderWithItems() {
        Customer customer = createTestCustomer();
        Product product1 = createTestProduct("Product 1", new BigDecimal("50.00"), 100);
        product1.setId(1L);
        Product product2 = createTestProduct("Product 2", new BigDecimal("75.00"), 50);
        product2.setId(2L);
        
        Order order = createTestOrder(customer, new BigDecimal("250.00"));
        
        List<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(createTestOrderItem(order, product1, 2)); // 100.00
        orderItems.add(createTestOrderItem(order, product2, 2)); // 150.00
        
        order.setOrderItems(orderItems);
        return order;
    }
    
    public static List<Customer> createTestCustomers() {
        List<Customer> customers = new ArrayList<>();
        customers.add(createTestCustomer("John", "Doe", "<EMAIL>"));
        customers.add(createTestCustomer("Jane", "Smith", "<EMAIL>"));
        customers.add(createTestCustomer("Bob", "Johnson", "<EMAIL>"));
        return customers;
    }
    
    public static List<Product> createTestProducts() {
        List<Product> products = new ArrayList<>();
        products.add(createTestProduct("Laptop", "LAP-001", new BigDecimal("999.99"), 10));
        products.add(createTestProduct("Mouse", "MOU-001", new BigDecimal("29.99"), 50));
        products.add(createTestProduct("Keyboard", "KEY-001", new BigDecimal("79.99"), 25));
        return products;
    }
}
