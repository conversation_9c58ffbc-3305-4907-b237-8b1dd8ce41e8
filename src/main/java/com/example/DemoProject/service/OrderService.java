package com.example.DemoProject.service;

import com.example.DemoProject.entity.Order;
import com.example.DemoProject.entity.OrderItem;
import com.example.DemoProject.entity.Product;
import com.example.DemoProject.entity.Customer;
import com.example.DemoProject.repository.OrderRepository;
import com.example.DemoProject.repository.OrderItemRepository;
import com.example.DemoProject.repository.ProductRepository;
import com.example.DemoProject.repository.CustomerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class OrderService {
    
    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final ProductRepository productRepository;
    private final CustomerRepository customerRepository;
    
    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }
    
    public Optional<Order> getOrderById(Long id) {
        return orderRepository.findById(id);
    }
    
    public Optional<Order> getOrderByIdWithItems(Long id) {
        return orderRepository.findByIdWithItems(id);
    }
    
    public Optional<Order> getOrderByIdWithFullDetails(Long id) {
        return orderRepository.findByIdWithFullDetails(id);
    }
    
    public List<Order> getOrdersByCustomerId(Long customerId) {
        return orderRepository.findByCustomerId(customerId);
    }
    
    public List<Order> getOrdersByStatus(Order.OrderStatus status) {
        return orderRepository.findByStatus(status);
    }
    
    public List<Order> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByOrderDateBetween(startDate, endDate);
    }
    
    public Order createOrder(Order order) {
        // Validate customer exists
        Customer customer = customerRepository.findById(order.getCustomer().getId())
                .orElseThrow(() -> new RuntimeException("Customer not found with id: " + order.getCustomer().getId()));
        
        order.setCustomer(customer);
        
        // Calculate total amount from order items
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            for (OrderItem item : order.getOrderItems()) {
                // Validate product exists and is active
                Product product = productRepository.findById(item.getProduct().getId())
                        .orElseThrow(() -> new RuntimeException("Product not found with id: " + item.getProduct().getId()));
                
                if (!product.getActive()) {
                    throw new RuntimeException("Product " + product.getName() + " is not active");
                }
                
                // Check stock availability
                if (product.getStockQuantity() < item.getQuantity()) {
                    throw new RuntimeException("Insufficient stock for product " + product.getName() + 
                                             ". Available: " + product.getStockQuantity() + ", Requested: " + item.getQuantity());
                }
                
                // Set unit price from product
                item.setUnitPrice(product.getPrice());
                item.setOrder(order);
                item.setProduct(product);
                
                // Calculate total price for this item
                BigDecimal itemTotal = item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity()));
                totalAmount = totalAmount.add(itemTotal);
                
                // Update product stock
                product.setStockQuantity(product.getStockQuantity() - item.getQuantity());
                productRepository.save(product);
            }
        }
        
        order.setTotalAmount(totalAmount);
        return orderRepository.save(order);
    }

    public Order updateOrderStatus(Long id, Order.OrderStatus status) {
        Order order = orderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Order not found with id: " + id));

        order.setStatus(status);
        return orderRepository.save(order);
    }

    public void cancelOrder(Long id) {
        Order order = orderRepository.findByIdWithItems(id)
                .orElseThrow(() -> new RuntimeException("Order not found with id: " + id));

        if (order.getStatus() == Order.OrderStatus.DELIVERED) {
            throw new RuntimeException("Cannot cancel a delivered order");
        }

        // Restore product stock
        if (order.getOrderItems() != null) {
            for (OrderItem item : order.getOrderItems()) {
                Product product = item.getProduct();
                product.setStockQuantity(product.getStockQuantity() + item.getQuantity());
                productRepository.save(product);
            }
        }

        order.setStatus(Order.OrderStatus.CANCELLED);
        orderRepository.save(order);
    }

    public void deleteOrder(Long id) {
        Order order = orderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Order not found with id: " + id));
        orderRepository.delete(order);
    }

    public Long getOrderCountByCustomerId(Long customerId) {
        return orderRepository.countByCustomerId(customerId);
    }

    public List<Order> getCustomerOrderHistory(Long customerId) {
        return orderRepository.findByCustomerIdOrderByOrderDateDesc(customerId);
    }
}
